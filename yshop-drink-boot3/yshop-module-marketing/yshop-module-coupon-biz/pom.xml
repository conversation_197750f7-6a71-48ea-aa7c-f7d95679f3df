<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>co.yixiang.boot</groupId>
        <artifactId>yshop-module-marketing</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yshop-module-coupon-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        优惠券 模块，主要实现商品购物车相关功能
    </description>

    <dependencies>
        <dependency>
            <groupId>co.yixiang.boot</groupId>
            <artifactId>yshop-module-coupon-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>co.yixiang.boot</groupId>
            <artifactId>yshop-module-store-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>co.yixiang.boot</groupId>
            <artifactId>yshop-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 业务组件 -->
<!--        <dependency>-->
<!--            <groupId>co.yixiang.boot</groupId>-->
<!--            <artifactId>yshop-spring-boot-starter-biz-operatelog</artifactId>-->
<!--        </dependency>-->

        <!-- Web 相关 -->
        <dependency>
            <groupId>co.yixiang.boot</groupId>
            <artifactId>yshop-spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>co.yixiang.boot</groupId>
            <artifactId>yshop-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>co.yixiang.boot</groupId>
            <artifactId>yshop-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>co.yixiang.boot</groupId>
            <artifactId>yshop-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>co.yixiang.boot</groupId>
            <artifactId>yshop-spring-boot-starter-excel</artifactId>
        </dependency>
    </dependencies>

</project>
