# 商户技术接入对接流程指导-餐饮零售

## 一、整体接入SOP
## 二、整体合作方案
## 三、环境搭建

### 1. 生成商户密钥
获取正式商编账号后需申请配置主商编密钥  
> **注意**：密钥类型需选择RSA2048国际密钥（不区分开发语言）


> **说明**：  
> - 生成的私钥由商户保留，配置到开发项目  
> - 生成的公钥需通过易宝商户后台上传（登录账号为正式商户编号）  

**加解密机制**：  
- 商户侧：商户私钥 + 易宝公钥  
- 易宝侧：商户公钥 + 易宝私钥  
- 易宝公钥：SDK模式自动封装，无需处理  
- 商户公钥：必须通过易宝后台上传验证

### 2. 下载开发SDK
1. **下载路径**：  
   易宝后台 → 开发者中心 → 开发服务 → 资源工具下载
2. **配置方式**：  
   https://open.yeepay.com/docs-v3/platform/201.md
   - 步骤1: 引入基础SDK
   - 步骤2: 拷贝业务SDK源码
   - 步骤3: RSA算法需额外引入依赖：
     ```xml
     <dependency>
        <groupId>com.yeepay.yop.sdk</groupId>
        <artifactId>yop-java-sdk-crypto-inter</artifactId>
        <version>${yop-java-sdk.version}</version>
     </dependency>
     ```

### 3. 配置SDK文件
**配置文件路径**：  
- `default`：默认配置文件  
- `template`：配置模板  

**关键参数**：  
| 参数 | 说明 |
|------|------|
| `app_key` | 主商编号 |
| `isv_private_key` | 商户私钥 |

**配置示例**：
```json
{
  "app_key": "app_商编",
  "isv_private_key": [{
    "store_type": "string",
    "cert_type": "RSA2048",
    "value": "商户私钥"
  }]
}
```

### 4. 结果通知解密
**Java解密方式**：  
https://open.yeepay.com/docs-v3/platform/207.md

> **注意事项**：  
> 1. 勿直接复制DEMO代码  
> 2. 测试需使用文档提供的测试密钥  
> 3. 使用工具类：`DigitalEnvelopeUtils.decrypt(response, appKey, "RSA2048")`

### 5. 无SDK模式
**测试类源码**：  
https://github.com/yop-platform/yop-java-sdk/blob/develop/yop-java-sdk-test/src/test/java/com/yeepay/yop/sdk/example/YopRsaEncryptExample.java  
**参考文档**：  
- https://open.yeepay.com/docs-v3/platform/202.md

## 四、接口对接
### 1. 入网
#### 1.1 商户类型
| 类型 | 定义 | 所需资料 |
|------|------|----------|
| 企业 | 有限公司/有限责任公司 | 营业执照、法人证件、银行账户、门店照片 |
| 个体工商户 | 个体户/个体经营 | 营业执照、经营者证件、银行账户、门店照片 |
| 小微商户 | 无营业执照商户 | 经营者身份证、银行卡、门店照片 |

#### 1.2 入网资料
| 类型 | 营业执照 | 开户许可证 | 法人身份证 | 银行卡 |
|------|----------|------------|------------|--------|
| 企业 | ✅ | ✅ | ✅ | ✅ |
| 个体工商户 | ✅ | ✅ | ✅ | ✅ |
| 小微商户 | ❌ | ❌ | ✅ | ✅ |

#### 1.3 结算账户配置
**非同名对私结算字段**：
| 参数 | 说明 | 必填 |
|------|------|------|
| `settlementDirection` | 结算方向 | 否 |
| `bankAccountType` | 账户类型 | 是 |
| `bankCardNo` | 银行卡号 | 是 |
| `bankCode` | 银行编码 | 条件 |
| `licenceNo` | 证件号码 | 条件 |
| `relation` | 经营人关系 | 条件 |

#### 1.4 接口列表
- **文件上传**：  
  `https://open.yeepay.com/docs/apis/fwssfk/options__yos__v1.0__sys__merchant__qual__upload`
- **企业入网**：  
  `https://open.yeepay.com/docs/apis/fwssfk/options__rest__v2.0__mer__register__saas__merchant`
- **小微入网**：  
  `https://open.yeepay.com/docs/apis/fwssfk/options__rest__v2.0__mer__register__saas__micro`

**产品信息模板**：
```json
[
  {"productCode":"WECHAT_OFFIACCOUNT_WECHAT_ONLINE", "percentRate":"0.6"},
  {"productCode":"MINI_PROGRAM_WECHAT_ONLINE", "percentRate":"0.6"},
  {"productCode":"BINDCARDPAY_DEBIT", "percentRate":"0.5"}
]
```

### 2. 微信/支付宝前置准备
#### 2.1 微信支付认证
**认证方式**：
1. **线下认证**：  
   渠道商后台获取二维码 → 商户扫码认证
2. **API认证**：  
   https://open.yeepay.com/docs-v3/product/fwssfk/2260.md

**公众号配置**：
- **API配置接口**：  
  `https://open.yeepay.com/docs/apis/fwssfk/options__rest__v2.0__aggpay__wechat-config__add`
- **配置要求**：
  - 小程序支付：需配置小程序appid
  - 公众号支付：需配置appid + 支付授权目录
  - 网页授权域名：`shouyin.yeepay.com/nc-cashier-wap/`

#### 2.2 易宝后台操作
**后台地址**：  
`https://mp.yeepay.com/auth/signin`  
**操作步骤**：
1. 配置APPID（小程序/公众号）
2. 上传MP文件（公众号后台获取）
3. 设置网页授权域名
4. 配置支付授权目录：
   - `https://cash.yeepay.com/newwap/`
   - `https://shouyin.yeepay.com/nc-cashier-wap/`
