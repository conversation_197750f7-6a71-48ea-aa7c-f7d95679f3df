/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */
/* 行为相关颜色 */
// $color-primary: #ADB838;
$color-primary: #09b4f1;
$color-success: #4cd964;
$color-warning: #FAB714;
$color-error: #D12E32;

/* 文字基本颜色 */
$text-color-base: #5A5B5C; //基本色
$text-color-assist: #919293; //辅助色
$text-color-black: #3B3C3E; //黑
$text-color-grey: #878889; //灰
$text-color-white: #ffffff; //白

/* 背景颜色 */
$bg-color: #F1F8FA;
$bg-color-grey: #F5F5F5;
//$bg-color-primary: #E8EACF;
$bg-color-primary: #dbe7ea;

/* 边框颜色 */
$border-color:#e2e2e2;

/* 尺寸变量 */

/* 文字尺寸 */
$font-size-sm:24rpx;
$font-size-base:28rpx;
$font-size-lg:32rpx;

/* 图片尺寸 */
$img-size-sm:40rpx;
$img-size-base:52rpx;
$img-size-lg:80rpx;

/* Border Radius */
$border-radius-sm: 4rpx;
$border-radius-base: 6rpx;
$border-radius-lg: 12rpx;
$border-radius-circle: 50%;

/* 水平间距 */
$spacing-row-sm: 10px;
$spacing-row-base: 20rpx;
$spacing-row-lg: 30rpx;

/* 垂直间距 */
$spacing-col-sm: 8rpx;
$spacing-col-base: 16rpx;
$spacing-col-lg: 24rpx;

/* 透明度 */
$opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$color-title: #2C405A; // 文章标题颜色
$font-size-title:40rpx;
$color-subtitle: #555555; // 二级标题颜色
$font-size-subtitle:36rpx;
$color-paragraph: #3F536E; // 文章段落颜色
$font-size-paragraph:30rpx;

$box-shadow: 0 20rpx 20rpx -20rpx rgba($color: #333, $alpha: 0.1);




$uv-main-color: #333333;
$uv-content-color: #999999;
$uv-tips-color: #909193;
$uv-light-color: #c0c4cc;
$uv-border-color: #dadbde;
$uv-bg-color: #f3f4f6;
$uv-disabled-color: #c8c9cc;

$uv-primary: #EE6D46;
$uv-primary-dark: #EE6D46;
$uv-primary-disabled: #FDEDE8;
$uv-primary-light: #ecf5ff;

$uv-warning: #f9ae3d;
$uv-warning-dark: #f1a532;
$uv-warning-disabled: #f9d39b;
$uv-warning-light: #fdf6ec;

$uv-success: #5ac725;
$uv-success-dark: #53c21d;
$uv-success-disabled: #a9e08f;
$uv-success-light: #f5fff0;

$uv-error: #f56c6c;
$uv-error-dark: #e45656;
$uv-error-disabled: #f7b2b2;
$uv-error-light: #fef0f0;

$uv-info: #909399;
$uv-info-dark: #767a82;
$uv-info-disabled: #c4c6c9;
$uv-info-light: #f4f4f5;


@import '~@/static/iconfont/iconfont.scss';

/* uni.scss */
// @import 'uview-ui/theme.scss';
